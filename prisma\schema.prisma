// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  xUsername     String?   @unique
  xUserId       String?   @unique
  totalPoints   Int       @default(0)
  rank          Int?
  joinDate      DateTime  @default(now())
  accounts      Account[]
  sessions      Session[]
  tweets        Tweet[]
  pointsHistory PointsHistory[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Tweet {
  id           String   @id @default(cuid())
  url          String   @unique
  content      String?
  userId       String
  likes        Int      @default(0)
  retweets     Int      @default(0)
  replies      Int      @default(0)
  basePoints   Int      @default(5)
  bonusPoints  Int      @default(0)
  totalPoints  Int      @default(5)
  isVerified   Boolean  @default(false)
  lastEngagementUpdate DateTime? // Track when engagement was last updated
  engagementUpdateCount Int @default(0) // Track how many times engagement was updated
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  pointsHistory PointsHistory[]
}

model PointsHistory {
  id           String   @id @default(cuid())
  userId       String
  tweetId      String?
  pointsAwarded Int
  reason       String
  createdAt    DateTime @default(now())

  user  User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  tweet Tweet? @relation(fields: [tweetId], references: [id], onDelete: Cascade)
}
